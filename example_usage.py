#!/usr/bin/env python3
"""
Example usage of the Test Ranking JSON Converter
Shows how to use the converter programmatically
"""

import json
from converter import TestRankingConverter


def example_basic_usage():
    """Basic usage example"""
    print("📚 Basic Usage Example")
    print("-" * 30)
    
    # Create converter instance
    converter = TestRankingConverter()
    
    # Convert files
    converter.convert_source_to_outputs(
        source_file="source.json",
        output_all_file="example_output_all.json",
        output_individual_files={
            "en": "example_output_en.json",
            "hi": "example_output_hi.json"
        }
    )
    
    print("✅ Conversion completed!")


def example_programmatic_usage():
    """Example of using converter methods programmatically"""
    print("\n🔧 Programmatic Usage Example")
    print("-" * 35)
    
    # Load source data
    with open("source.json", 'r', encoding='utf-8') as f:
        source_data = json.load(f)
    
    # Create converter
    converter = TestRankingConverter()
    
    # Convert to all-languages format
    all_languages_data = converter.convert_to_all_languages(source_data)
    print(f"✅ Converted to all-languages format")
    print(f"   - Test name: {all_languages_data['name']}")
    print(f"   - Total questions: {all_languages_data['total_questions']}")
    print(f"   - Sections: {len(all_languages_data['sections'])}")
    
    # Convert to individual languages
    for lang_code in ["en", "hi"]:
        single_lang_data = converter.convert_to_single_language(all_languages_data, lang_code)
        lang_name = converter.language_names[lang_code]
        print(f"✅ Converted to {lang_name} format")
        print(f"   - Language: {single_lang_data['languages']}")
        print(f"   - Primary language: {single_lang_data['primary_language']}")


def example_data_inspection():
    """Example of inspecting converted data"""
    print("\n🔍 Data Inspection Example")
    print("-" * 30)
    
    # Load converted data
    with open("output_all_converted.json", 'r', encoding='utf-8') as f:
        all_data = json.load(f)
    
    with open("output_hi_converted.json", 'r', encoding='utf-8') as f:
        hi_data = json.load(f)
    
    print("📊 All-Languages Data:")
    print(f"   - Name: {all_data['name']}")
    print(f"   - Duration: {all_data['duration']} minutes")
    print(f"   - Total marks: {all_data['marks']}")
    print(f"   - Languages: {list(all_data['languages'].keys())}")
    
    # Show first question in both languages
    if all_data['sections'] and all_data['sections'][0]['question_list']:
        first_q = all_data['sections'][0]['question_list'][0]
        print(f"\n📝 First Question (All Languages):")
        print(f"   - Type: {first_q['type']}")
        print(f"   - Answer: {first_q['answer']}")
        print(f"   - English: {first_q['question']['en'][:100]}...")
        print(f"   - Hindi: {first_q['question']['hi'][:100]}...")
    
    print(f"\n📊 Hindi-Only Data:")
    print(f"   - Language: {hi_data['languages']}")
    print(f"   - Primary: {hi_data['primary_language']}")
    
    # Show first question in Hindi only
    if hi_data['sections'] and hi_data['sections'][0]['question_list']:
        first_q_hi = hi_data['sections'][0]['question_list'][0]
        print(f"\n📝 First Question (Hindi Only):")
        print(f"   - Question: {first_q_hi['question'][:100]}...")
        print(f"   - Options: {len(first_q_hi['options'])} choices")


def example_custom_converter():
    """Example of creating a custom converter with different settings"""
    print("\n⚙️ Custom Converter Example")
    print("-" * 30)
    
    class CustomConverter(TestRankingConverter):
        def __init__(self):
            super().__init__()
            # Add support for more languages if needed
            # self.languages = ["en", "hi", "es"]  # Add Spanish
            # self.language_names = {"en": "English", "hi": "Hindi", "es": "Spanish"}
        
        def get_marks(self, question_data):
            """Custom marks calculation"""
            marks = super().get_marks(question_data)
            # Could apply custom logic here
            return marks
        
        def get_question_type(self, question_data):
            """Custom question type detection"""
            # Could add more sophisticated type detection
            return "mcq_single_correct"
    
    # Use custom converter
    custom_converter = CustomConverter()
    print(f"✅ Created custom converter")
    print(f"   - Supported languages: {custom_converter.languages}")
    print(f"   - Language names: {custom_converter.language_names}")


def main():
    """Run all examples"""
    print("🚀 Test Ranking Converter - Usage Examples")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_programmatic_usage()
        example_data_inspection()
        example_custom_converter()
        
        print("\n" + "=" * 50)
        print("🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
