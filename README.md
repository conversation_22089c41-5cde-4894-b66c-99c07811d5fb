# Test Ranking JSON Converter

This Python script converts Test Ranking JSON data from the source format to multiple output formats.

## Overview

The converter transforms a source JSON file containing multilingual test data into:
1. **All Languages JSON** - Contains all languages with nested language objects
2. **Individual Language JSONs** - Separate files for each language without language keys

## Source Format Analysis

### Source JSON Structure (`source.json`)
- Contains multilingual data with separate fields for each language
- Fields like `question_en`, `question_hi`, `option_en_1`, `option_hi_1`, etc.
- Has encrypted question IDs (`q1_en`, `q2_en`, etc.)
- Contains comprehensive text for question groups
- Detailed metadata: `section_id`, `topic_id`, `marks`, `answer_type`, etc.
- Numeric answers stored as strings

### Key Fields in Source:
- `section_name`: Name of the test section
- `series_name`: Name of the test series
- `series_time`: Duration in minutes
- `all_questions[]`: Array of question objects
- `question_en/hi`: Question text in English/Hindi
- `comprehensive_en/hi`: Pre-text/directions for question groups
- `option_en/hi_1-5`: Multiple choice options
- `solution_en/hi`: Solution explanations
- `answer_en/hi`: Correct answer number (as string)
- `marks`: Points for correct answer

## Output Formats

### 1. All Languages JSON (`output_all_converted.json`)
```json
{
  "name": "Test Name",
  "duration": 6.0,
  "marks": 200,
  "total_questions": 100,
  "sections": [
    {
      "section_name": "Section Name",
      "section_questions": 25,
      "section_marks": 50.0,
      "pre": [
        {
          "questions": [1, 2, 3],
          "text": {
            "en": "English directions",
            "hi": "Hindi directions"
          }
        }
      ],
      "question_list": [
        {
          "type": "mcq_single_correct",
          "question": {
            "en": "English question",
            "hi": "Hindi question"
          },
          "options": {
            "en": ["Option 1", "Option 2", "Option 3", "Option 4"],
            "hi": ["विकल्प 1", "विकल्प 2", "विकल्प 3", "विकल्प 4"]
          },
          "answer": 1,
          "solution": {
            "en": "English solution",
            "hi": "Hindi solution"
          },
          "positive_marks": 2.0,
          "negative_makrs": 0.25
        }
      ]
    }
  ],
  "instructions": {
    "en": "",
    "hi": ""
  },
  "languages": {
    "en": "English",
    "hi": "Hindi"
  },
  "primary_language": "en"
}
```

### 2. Individual Language JSON (`output_hi_converted.json`)
```json
{
  "name": "Test Name",
  "duration": 6.0,
  "marks": 200,
  "total_questions": 100,
  "sections": [
    {
      "section_name": "Section Name",
      "section_questions": 25,
      "section_marks": 50.0,
      "pre": [
        {
          "questions": [1, 2, 3],
          "text": "Hindi directions"
        }
      ],
      "question_list": [
        {
          "type": "mcq_single_correct",
          "question": "Hindi question",
          "options": ["विकल्प 1", "विकल्प 2", "विकल्प 3", "विकल्प 4"],
          "answer": 1,
          "solution": "Hindi solution",
          "positive_marks": 2.0,
          "negative_makrs": 0.25
        }
      ]
    }
  ],
  "instructions": "",
  "languages": "Hindi",
  "primary_language": "hi"
}
```

## Key Differences

### All Languages vs Individual Language:
1. **Language Objects**: All-languages uses `{"en": "text", "hi": "text"}`, individual uses direct strings
2. **Languages Field**: All-languages has object `{"en": "English", "hi": "Hindi"}`, individual has string `"Hindi"`
3. **Content**: Individual files contain only the target language content

### Source vs Output:
1. **Structure**: Source has flat language-specific fields, output has nested structure
2. **Grouping**: Output groups questions by comprehensive text in `pre` sections
3. **Answer Format**: Source has string answers, output has numeric answers
4. **Question Type**: Output adds explicit `type` field
5. **Marks**: Output separates positive and negative marks

## Usage

```bash
python converter.py
```

This will:
1. Read `source.json`
2. Generate `output_all_converted.json` (all languages)
3. Generate `output_en_converted.json` (English only)
4. Generate `output_hi_converted.json` (Hindi only)

## Features

- **Comprehensive Text Grouping**: Automatically groups questions sharing the same comprehensive/directions text
- **Language Detection**: Supports English (`en`) and Hindi (`hi`)
- **Error Handling**: Validates input format and handles missing fields gracefully
- **Flexible Output**: Generates both multilingual and single-language formats
- **Metadata Preservation**: Maintains test duration, marks, and section information
- **UTF-8 Support**: Properly handles Unicode characters in Hindi text

## Requirements

- Python 3.6+
- No external dependencies (uses only standard library)

## File Structure

```
converter/
├── converter.py           # Main converter script
├── source.json           # Input file (source format)
├── output_all_converted.json    # Generated all-languages output
├── output_en_converted.json     # Generated English output
├── output_hi_converted.json     # Generated Hindi output
└── README.md             # This documentation
```
