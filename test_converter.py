#!/usr/bin/env python3
"""
Test script for the Test Ranking JSON Converter
Validates the conversion results and checks format compliance
"""

import json
import sys
from pathlib import Path


def load_json(file_path):
    """Load JSON file safely"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {file_path}: {e}")
        return None


def test_all_languages_format(data):
    """Test all-languages JSON format"""
    print("\n🔍 Testing All-Languages Format...")
    
    # Check top-level structure
    required_fields = ["name", "duration", "marks", "total_questions", "sections", "instructions", "languages", "primary_language"]
    for field in required_fields:
        if field not in data:
            print(f"❌ Missing required field: {field}")
            return False
    
    # Check languages field structure
    if not isinstance(data["languages"], dict):
        print("❌ 'languages' should be an object")
        return False
    
    if "en" not in data["languages"] or "hi" not in data["languages"]:
        print("❌ 'languages' should contain 'en' and 'hi' keys")
        return False
    
    # Check instructions structure
    if not isinstance(data["instructions"], dict):
        print("❌ 'instructions' should be an object")
        return False
    
    if "en" not in data["instructions"] or "hi" not in data["instructions"]:
        print("❌ 'instructions' should contain 'en' and 'hi' keys")
        return False
    
    # Check sections
    if not isinstance(data["sections"], list) or len(data["sections"]) == 0:
        print("❌ 'sections' should be a non-empty array")
        return False
    
    # Check first section structure
    section = data["sections"][0]
    section_fields = ["section_name", "section_questions", "section_marks", "pre", "question_list"]
    for field in section_fields:
        if field not in section:
            print(f"❌ Missing section field: {field}")
            return False
    
    # Check question structure
    if len(section["question_list"]) > 0:
        question = section["question_list"][0]
        question_fields = ["type", "question", "options", "answer", "solution", "positive_marks", "negative_makrs"]
        for field in question_fields:
            if field not in question:
                print(f"❌ Missing question field: {field}")
                return False
        
        # Check language structure in question
        if not isinstance(question["question"], dict):
            print("❌ Question text should be a language object")
            return False
        
        if "en" not in question["question"] or "hi" not in question["question"]:
            print("❌ Question should contain 'en' and 'hi' keys")
            return False
        
        # Check options structure
        if not isinstance(question["options"], dict):
            print("❌ Options should be a language object")
            return False
        
        if "en" not in question["options"] or "hi" not in question["options"]:
            print("❌ Options should contain 'en' and 'hi' keys")
            return False
        
        if not isinstance(question["options"]["en"], list):
            print("❌ Options should be arrays")
            return False
    
    print("✅ All-languages format is valid")
    return True


def test_single_language_format(data, language_code):
    """Test single-language JSON format"""
    print(f"\n🔍 Testing {language_code.upper()} Single-Language Format...")
    
    # Check top-level structure
    required_fields = ["name", "duration", "marks", "total_questions", "sections", "instructions", "languages", "primary_language"]
    for field in required_fields:
        if field not in data:
            print(f"❌ Missing required field: {field}")
            return False
    
    # Check languages field structure (should be string)
    if not isinstance(data["languages"], str):
        print("❌ 'languages' should be a string in single-language format")
        return False
    
    # Check instructions structure (should be string)
    if not isinstance(data["instructions"], str):
        print("❌ 'instructions' should be a string in single-language format")
        return False
    
    # Check primary language
    if data["primary_language"] != language_code:
        print(f"❌ Primary language should be '{language_code}', got '{data['primary_language']}'")
        return False
    
    # Check sections
    if not isinstance(data["sections"], list) or len(data["sections"]) == 0:
        print("❌ 'sections' should be a non-empty array")
        return False
    
    # Check first section structure
    section = data["sections"][0]
    section_fields = ["section_name", "section_questions", "section_marks", "pre", "question_list"]
    for field in section_fields:
        if field not in section:
            print(f"❌ Missing section field: {field}")
            return False
    
    # Check question structure
    if len(section["question_list"]) > 0:
        question = section["question_list"][0]
        question_fields = ["type", "question", "options", "answer", "solution", "positive_marks", "negative_makrs"]
        for field in question_fields:
            if field not in question:
                print(f"❌ Missing question field: {field}")
                return False
        
        # Check that question text is a string (not language object)
        if not isinstance(question["question"], str):
            print("❌ Question text should be a string in single-language format")
            return False
        
        # Check options structure (should be array of strings)
        if not isinstance(question["options"], list):
            print("❌ Options should be an array in single-language format")
            return False
        
        if len(question["options"]) > 0 and not isinstance(question["options"][0], str):
            print("❌ Options should be strings in single-language format")
            return False
        
        # Check solution is string
        if not isinstance(question["solution"], str):
            print("❌ Solution should be a string in single-language format")
            return False
    
    print(f"✅ {language_code.upper()} single-language format is valid")
    return True


def test_data_consistency(all_data, en_data, hi_data):
    """Test consistency between all-languages and single-language files"""
    print("\n🔍 Testing Data Consistency...")
    
    # Check basic metadata consistency
    if all_data["name"] != en_data["name"] or all_data["name"] != hi_data["name"]:
        print("❌ Test names are inconsistent")
        return False
    
    if all_data["total_questions"] != en_data["total_questions"] or all_data["total_questions"] != hi_data["total_questions"]:
        print("❌ Total questions count is inconsistent")
        return False
    
    if len(all_data["sections"]) != len(en_data["sections"]) or len(all_data["sections"]) != len(hi_data["sections"]):
        print("❌ Number of sections is inconsistent")
        return False
    
    # Check first section consistency
    if len(all_data["sections"]) > 0:
        all_section = all_data["sections"][0]
        en_section = en_data["sections"][0]
        hi_section = hi_data["sections"][0]
        
        if all_section["section_questions"] != en_section["section_questions"] or all_section["section_questions"] != hi_section["section_questions"]:
            print("❌ Section question counts are inconsistent")
            return False
        
        # Check first question consistency
        if len(all_section["question_list"]) > 0:
            all_q = all_section["question_list"][0]
            en_q = en_section["question_list"][0]
            hi_q = hi_section["question_list"][0]
            
            if all_q["answer"] != en_q["answer"] or all_q["answer"] != hi_q["answer"]:
                print("❌ Answer numbers are inconsistent")
                return False
            
            if all_q["positive_marks"] != en_q["positive_marks"] or all_q["positive_marks"] != hi_q["positive_marks"]:
                print("❌ Positive marks are inconsistent")
                return False
            
            # Check language content extraction
            if all_q["question"]["en"] != en_q["question"]:
                print("❌ English question text extraction failed")
                return False
            
            if all_q["question"]["hi"] != hi_q["question"]:
                print("❌ Hindi question text extraction failed")
                return False
    
    print("✅ Data consistency check passed")
    return True


def main():
    """Main test function"""
    print("🧪 Testing Test Ranking JSON Converter")
    print("=" * 50)
    
    # Load test files
    all_data = load_json("output_all_converted.json")
    en_data = load_json("output_en_converted.json")
    hi_data = load_json("output_hi_converted.json")
    
    if not all([all_data, en_data, hi_data]):
        print("❌ Failed to load test files")
        return 1
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    if test_all_languages_format(all_data):
        tests_passed += 1
    
    if test_single_language_format(en_data, "en"):
        tests_passed += 1
    
    if test_single_language_format(hi_data, "hi"):
        tests_passed += 1
    
    if test_data_consistency(all_data, en_data, hi_data):
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Converter is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the converter.")
        return 1


if __name__ == "__main__":
    exit(main())
