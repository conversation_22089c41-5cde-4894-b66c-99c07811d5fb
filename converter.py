#!/usr/bin/env python3
"""
JSON Converter for Test Ranking Data
Converts source.json format to multiple output formats:
1. output_all.json - Contains all languages with nested language objects
2. output_{lang}.json - Individual language files without language keys
"""

import json
import re
from typing import Dict, List, Any, Optional
from collections import defaultdict


class TestRankingConverter:
    def __init__(self):
        self.languages = ["en", "hi"]  # English and Hindi
        self.language_names = {"en": "English", "hi": "Hindi"}
        
    def extract_comprehensive_text(self, question_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract comprehensive text for both languages"""
        comprehensive = {}
        for lang in self.languages:
            comp_key = f"comprehensive_{lang}"
            if comp_key in question_data and question_data[comp_key].strip():
                comprehensive[lang] = question_data[comp_key].strip()
            else:
                comprehensive[lang] = ""
        return comprehensive
    
    def extract_question_text(self, question_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract question text for both languages"""
        question = {}
        for lang in self.languages:
            q_key = f"question_{lang}"
            if q_key in question_data:
                question[lang] = question_data[q_key].strip()
            else:
                question[lang] = ""
        return question
    
    def extract_options(self, question_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract options for both languages"""
        options = {lang: [] for lang in self.languages}
        
        for i in range(1, 6):  # options 1-5
            for lang in self.languages:
                opt_key = f"option_{lang}_{i}"
                if opt_key in question_data and question_data[opt_key].strip():
                    options[lang].append(question_data[opt_key].strip())
        
        return options
    
    def extract_solution(self, question_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract solution text for both languages"""
        solution = {}
        for lang in self.languages:
            sol_key = f"solution_{lang}"
            if sol_key in question_data:
                solution[lang] = question_data[sol_key].strip()
            else:
                solution[lang] = ""
        return solution
    
    def get_answer_number(self, question_data: Dict[str, Any]) -> int:
        """Extract answer number (convert from string to int)"""
        answer_en = question_data.get("answer_en", "1")
        try:
            return int(answer_en)
        except (ValueError, TypeError):
            return 1
    
    def get_question_type(self, question_data: Dict[str, Any]) -> str:
        """Determine question type based on answer_type"""
        answer_type = question_data.get("answer_type", "optional")
        if answer_type == "optional":
            return "mcq_single_correct"
        return "mcq_single_correct"  # default
    
    def get_marks(self, question_data: Dict[str, Any]) -> float:
        """Extract marks for the question"""
        marks = question_data.get("marks", "2")
        try:
            return float(marks)
        except (ValueError, TypeError):
            return 2.0
    
    def group_comprehensive_texts(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Group questions by their comprehensive text to create pre-text sections"""
        comprehensive_groups = defaultdict(list)
        
        for i, question in enumerate(questions, 1):
            comprehensive = self.extract_comprehensive_text(question)
            # Use English comprehensive as key for grouping
            comp_key = comprehensive.get("en", "").strip()
            if comp_key:
                comprehensive_groups[comp_key].append({
                    "question_num": i,
                    "comprehensive": comprehensive
                })
        
        pre_sections = []
        for comp_text, group_questions in comprehensive_groups.items():
            if comp_text:  # Only add if there's actual comprehensive text
                question_nums = [q["question_num"] for q in group_questions]
                comprehensive_texts = group_questions[0]["comprehensive"]  # All have same comprehensive
                
                pre_sections.append({
                    "questions": question_nums,
                    "text": comprehensive_texts
                })
        
        return pre_sections
    
    def convert_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert a single question from source format to output format"""
        question_text = self.extract_question_text(question_data)
        options = self.extract_options(question_data)
        solution = self.extract_solution(question_data)
        answer = self.get_answer_number(question_data)
        question_type = self.get_question_type(question_data)
        marks = self.get_marks(question_data)
        
        return {
            "type": question_type,
            "question": question_text,
            "options": options,
            "answer": answer,
            "solution": solution,
            "positive_marks": marks,
            "negative_makrs": 0.25  # Default negative marking
        }
    
    def convert_to_all_languages(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert source data to all-languages format"""
        if "data" not in source_data or not source_data["data"]:
            raise ValueError("Invalid source data format")
        
        sections = []
        total_questions = 0
        total_marks = 0.0
        
        for section_data in source_data["data"]:
            section_name = section_data.get("section_name", "Unknown Section")
            questions = section_data.get("all_questions", [])
            
            if not questions:
                continue
            
            # Convert questions
            converted_questions = []
            section_marks = 0.0
            
            for question in questions:
                converted_q = self.convert_question(question)
                converted_questions.append(converted_q)
                section_marks += converted_q["positive_marks"]
            
            # Group comprehensive texts for pre-sections
            pre_sections = self.group_comprehensive_texts(questions)
            
            sections.append({
                "section_name": section_name,
                "section_questions": len(converted_questions),
                "section_marks": section_marks,
                "pre": pre_sections,
                "question_list": converted_questions
            })
            
            total_questions += len(converted_questions)
            total_marks += section_marks
        
        # Get series info
        series_name = "Test App"  # Default name
        series_time = 60  # Default duration
        
        if source_data["data"]:
            first_section = source_data["data"][0]
            if "series_name" in first_section:
                series_name = first_section["series_name"]
            if "series_time" in first_section:
                try:
                    series_time = float(first_section["series_time"]) / 10  # Convert minutes to appropriate format
                except (ValueError, TypeError):
                    series_time = 6.0
        
        return {
            "name": series_name,
            "duration": series_time,
            "marks": int(total_marks),
            "total_questions": total_questions,
            "sections": sections,
            "instructions": {
                "en": "",
                "hi": ""
            },
            "languages": self.language_names,
            "primary_language": "en"
        }

    def convert_to_single_language(self, all_languages_data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
        """Convert all-languages format to single language format"""
        if target_language not in self.languages:
            raise ValueError(f"Unsupported language: {target_language}")

        def extract_language_content(content):
            """Recursively extract content for target language"""
            if isinstance(content, dict):
                if all(key in self.languages for key in content.keys()):
                    # This is a language object, extract target language
                    return content.get(target_language, "")
                else:
                    # Regular object, process recursively
                    result = {}
                    for key, value in content.items():
                        result[key] = extract_language_content(value)
                    return result
            elif isinstance(content, list):
                return [extract_language_content(item) for item in content]
            else:
                return content

        # Deep copy and convert
        single_lang_data = extract_language_content(all_languages_data)

        # Update language-specific fields
        single_lang_data["languages"] = self.language_names[target_language]
        single_lang_data["primary_language"] = target_language

        return single_lang_data

    def convert_source_to_outputs(self, source_file: str, output_all_file: str, output_individual_files: Dict[str, str]):
        """Main conversion method"""
        print(f"Reading source file: {source_file}")

        # Read source data
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                source_data = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Source file not found: {source_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in source file: {e}")

        print("Converting to all-languages format...")
        # Convert to all-languages format
        all_languages_data = self.convert_to_all_languages(source_data)

        # Save all-languages file
        print(f"Saving all-languages file: {output_all_file}")
        with open(output_all_file, 'w', encoding='utf-8') as f:
            json.dump(all_languages_data, f, indent=2, ensure_ascii=False)

        # Convert and save individual language files
        for lang_code, output_file in output_individual_files.items():
            if lang_code in self.languages:
                print(f"Converting to {lang_code} format...")
                single_lang_data = self.convert_to_single_language(all_languages_data, lang_code)

                print(f"Saving {lang_code} file: {output_file}")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(single_lang_data, f, indent=2, ensure_ascii=False)
            else:
                print(f"Warning: Unsupported language code: {lang_code}")

        print("Conversion completed successfully!")


def main():
    """Main function to run the converter"""
    converter = TestRankingConverter()

    # Define file paths
    source_file = "source.json"
    output_all_file = "output_all_converted.json"
    output_individual_files = {
        "en": "output_en_converted.json",
        "hi": "output_hi_converted.json"
    }

    try:
        converter.convert_source_to_outputs(
            source_file=source_file,
            output_all_file=output_all_file,
            output_individual_files=output_individual_files
        )

        print("\n" + "="*50)
        print("CONVERSION SUMMARY:")
        print("="*50)
        print(f"✓ Source file: {source_file}")
        print(f"✓ All languages output: {output_all_file}")
        for lang, file in output_individual_files.items():
            print(f"✓ {lang.upper()} language output: {file}")
        print("="*50)

    except Exception as e:
        print(f"Error during conversion: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
